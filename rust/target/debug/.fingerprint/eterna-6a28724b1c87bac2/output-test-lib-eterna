{"$message_type":"diagnostic","message":"unused import: `std::collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":3975,"byte_end":4000,"line_start":134,"line_end":134,"column_start":9,"column_end":34,"is_primary":true,"text":[{"text":"    use std::collections::HashMap;","highlight_start":9,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":3971,"byte_end":4001,"line_start":134,"line_end":134,"column_start":5,"column_end":35,"is_primary":true,"text":[{"text":"    use std::collections::HashMap;","highlight_start":5,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::collections::HashMap`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:134:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m134\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    use std::collections::HashMap;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `object_dict_of_addresses_and_names`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/utils_parsers.rs","byte_start":4329,"byte_end":4363,"line_start":180,"line_end":180,"column_start":5,"column_end":39,"is_primary":true,"text":[{"text":"    object_dict_of_addresses_and_names: &HashMap<String, String>,","highlight_start":5,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/utils_parsers.rs","byte_start":4329,"byte_end":4363,"line_start":180,"line_end":180,"column_start":5,"column_end":39,"is_primary":true,"text":[{"text":"    object_dict_of_addresses_and_names: &HashMap<String, String>,","highlight_start":5,"highlight_end":39}],"label":null,"suggested_replacement":"_object_dict_of_addresses_and_names","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `object_dict_of_addresses_and_names`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/utils_parsers.rs:180:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m180\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    object_dict_of_addresses_and_names: &HashMap<String, String>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_object_dict_of_addresses_and_names`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `object_dict_of_addresses_and_names`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/utils_parsers.rs","byte_start":6600,"byte_end":6634,"line_start":246,"line_end":246,"column_start":5,"column_end":39,"is_primary":true,"text":[{"text":"    object_dict_of_addresses_and_names: &HashMap<String, String>,","highlight_start":5,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/utils_parsers.rs","byte_start":6600,"byte_end":6634,"line_start":246,"line_end":246,"column_start":5,"column_end":39,"is_primary":true,"text":[{"text":"    object_dict_of_addresses_and_names: &HashMap<String, String>,","highlight_start":5,"highlight_end":39}],"label":null,"suggested_replacement":"_object_dict_of_addresses_and_names","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `object_dict_of_addresses_and_names`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/utils_parsers.rs:246:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m246\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    object_dict_of_addresses_and_names: &HashMap<String, String>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_object_dict_of_addresses_and_names`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `tracking_id` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src/utils_parsers.rs","byte_start":10921,"byte_end":10932,"line_start":352,"line_end":352,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"    let mut tracking_id = String::new();","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(unused_assignments)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: value assigned to `tracking_id` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/utils_parsers.rs:352:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m352\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut tracking_id = String::new();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_assignments)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `real_interface` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src/utils_parsers.rs","byte_start":10962,"byte_end":10976,"line_start":353,"line_end":353,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"    let mut real_interface = String::new();","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: value assigned to `real_interface` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/utils_parsers.rs:353:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m353\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut real_interface = String::new();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `reason` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src/utils_parsers.rs","byte_start":11006,"byte_end":11012,"line_start":354,"line_end":354,"column_start":13,"column_end":19,"is_primary":true,"text":[{"text":"    let mut reason = String::new();","highlight_start":13,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: value assigned to `reason` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/utils_parsers.rs:354:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m354\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut reason = String::new();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `action` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src/utils_parsers.rs","byte_start":11042,"byte_end":11048,"line_start":355,"line_end":355,"column_start":13,"column_end":19,"is_primary":true,"text":[{"text":"    let mut action = String::new();","highlight_start":13,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: value assigned to `action` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/utils_parsers.rs:355:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m355\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut action = String::new();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `direction` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src/utils_parsers.rs","byte_start":11078,"byte_end":11087,"line_start":356,"line_end":356,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"    let mut direction = String::new();","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: value assigned to `direction` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/utils_parsers.rs:356:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m356\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut direction = String::new();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `ip_version` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/utils_parsers.rs","byte_start":11117,"byte_end":11127,"line_start":357,"line_end":357,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"    let mut ip_version = String::new();","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_ip_version` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable `ip_version` is assigned to, but never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/utils_parsers.rs:357:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m357\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut ip_version = String::new();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: consider using `_ip_version` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `protocol_name` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src/utils_parsers.rs","byte_start":11466,"byte_end":11479,"line_start":368,"line_end":368,"column_start":13,"column_end":26,"is_primary":true,"text":[{"text":"    let mut protocol_name = String::new();","highlight_start":13,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: value assigned to `protocol_name` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/utils_parsers.rs:368:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m368\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut protocol_name = String::new();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `source_ip` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src/utils_parsers.rs","byte_start":11540,"byte_end":11549,"line_start":370,"line_end":370,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"    let mut source_ip = String::new();","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: value assigned to `source_ip` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/utils_parsers.rs:370:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m370\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut source_ip = String::new();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `destination_ip` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src/utils_parsers.rs","byte_start":11579,"byte_end":11593,"line_start":371,"line_end":371,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"    let mut destination_ip = String::new();","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: value assigned to `destination_ip` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/utils_parsers.rs:371:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m371\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut destination_ip = String::new();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `ip_version` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src/utils_parsers.rs","byte_start":12790,"byte_end":12800,"line_start":398,"line_end":398,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"        ip_version     = ip_version_str.to_string();","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: value assigned to `ip_version` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/utils_parsers.rs:398:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m398\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        ip_version     = ip_version_str.to_string();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `protocol_name_str`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/utils_parsers.rs","byte_start":13610,"byte_end":13627,"line_start":419,"line_end":419,"column_start":51,"column_end":68,"is_primary":true,"text":[{"text":"            if let (Some(&protocol_id_str), Some(&protocol_name_str)) = (rest_splited.next(), rest_splited.next()) {","highlight_start":51,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/utils_parsers.rs","byte_start":13610,"byte_end":13627,"line_start":419,"line_end":419,"column_start":51,"column_end":68,"is_primary":true,"text":[{"text":"            if let (Some(&protocol_id_str), Some(&protocol_name_str)) = (rest_splited.next(), rest_splited.next()) {","highlight_start":51,"highlight_end":68}],"label":null,"suggested_replacement":"_protocol_name_str","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `protocol_name_str`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/utils_parsers.rs:419:51\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m419\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            if let (Some(&protocol_id_str), Some(&protocol_name_str)) = (rest_splited.next(), rest_splited.next()) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_protocol_name_str`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `object_dict_of_addresses_and_names`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/utils_parsers.rs","byte_start":29820,"byte_end":29854,"line_start":983,"line_end":983,"column_start":5,"column_end":39,"is_primary":true,"text":[{"text":"    object_dict_of_addresses_and_names: &HashMap<String, String>,","highlight_start":5,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/utils_parsers.rs","byte_start":29820,"byte_end":29854,"line_start":983,"line_end":983,"column_start":5,"column_end":39,"is_primary":true,"text":[{"text":"    object_dict_of_addresses_and_names: &HashMap<String, String>,","highlight_start":5,"highlight_end":39}],"label":null,"suggested_replacement":"_object_dict_of_addresses_and_names","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `object_dict_of_addresses_and_names`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/utils_parsers.rs:983:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m983\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    object_dict_of_addresses_and_names: &HashMap<String, String>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_object_dict_of_addresses_and_names`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnDaemon__test_valid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":10447,"byte_end":10481,"line_start":254,"line_end":254,"column_start":8,"column_end":42,"is_primary":true,"text":[{"text":"    fn TestParseLnDaemon__test_valid_line() {","highlight_start":8,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(non_snake_case)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":10447,"byte_end":10481,"line_start":254,"line_end":254,"column_start":8,"column_end":42,"is_primary":true,"text":[{"text":"    fn TestParseLnDaemon__test_valid_line() {","highlight_start":8,"highlight_end":42}],"label":null,"suggested_replacement":"test_parse_ln_daemon_test_valid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnDaemon__test_valid_line` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:254:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m254\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnDaemon__test_valid_line() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_daemon_test_valid_line`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(non_snake_case)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnDaemon__test_mapping_object_ip_to_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":11590,"byte_end":11646,"line_start":288,"line_end":288,"column_start":8,"column_end":64,"is_primary":true,"text":[{"text":"    fn TestParseLnDaemon__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":64}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":11590,"byte_end":11646,"line_start":288,"line_end":288,"column_start":8,"column_end":64,"is_primary":true,"text":[{"text":"    fn TestParseLnDaemon__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":64}],"label":null,"suggested_replacement":"test_parse_ln_daemon_test_mapping_object_ip_to_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnDaemon__test_mapping_object_ip_to_object_name` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:288:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m288\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnDaemon__test_mapping_object_ip_to_object_name() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_daemon_test_mapping_object_ip_to_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnDaemon__test_invalid_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":12758,"byte_end":12801,"line_start":322,"line_end":322,"column_start":8,"column_end":51,"is_primary":true,"text":[{"text":"    fn TestParseLnDaemon__test_invalid_object_name() {","highlight_start":8,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":12758,"byte_end":12801,"line_start":322,"line_end":322,"column_start":8,"column_end":51,"is_primary":true,"text":[{"text":"    fn TestParseLnDaemon__test_invalid_object_name() {","highlight_start":8,"highlight_end":51}],"label":null,"suggested_replacement":"test_parse_ln_daemon_test_invalid_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnDaemon__test_invalid_object_name` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:322:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m322\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnDaemon__test_invalid_object_name() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_daemon_test_invalid_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnDaemon__test_invalid_event_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":13490,"byte_end":13532,"line_start":347,"line_end":347,"column_start":8,"column_end":50,"is_primary":true,"text":[{"text":"    fn TestParseLnDaemon__test_invalid_event_type() {","highlight_start":8,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":13490,"byte_end":13532,"line_start":347,"line_end":347,"column_start":8,"column_end":50,"is_primary":true,"text":[{"text":"    fn TestParseLnDaemon__test_invalid_event_type() {","highlight_start":8,"highlight_end":50}],"label":null,"suggested_replacement":"test_parse_ln_daemon_test_invalid_event_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnDaemon__test_invalid_event_type` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:347:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m347\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnDaemon__test_invalid_event_type() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_daemon_test_invalid_event_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnDaemon__test_invalid_alert_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":13967,"byte_end":14009,"line_start":362,"line_end":362,"column_start":8,"column_end":50,"is_primary":true,"text":[{"text":"    fn TestParseLnDaemon__test_invalid_alert_type() {","highlight_start":8,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":13967,"byte_end":14009,"line_start":362,"line_end":362,"column_start":8,"column_end":50,"is_primary":true,"text":[{"text":"    fn TestParseLnDaemon__test_invalid_alert_type() {","highlight_start":8,"highlight_end":50}],"label":null,"suggested_replacement":"test_parse_ln_daemon_test_invalid_alert_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnDaemon__test_invalid_alert_type` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:362:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m362\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnDaemon__test_invalid_alert_type() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_daemon_test_invalid_alert_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnDaemon__test_invalid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":14057,"byte_end":14093,"line_start":367,"line_end":367,"column_start":8,"column_end":44,"is_primary":true,"text":[{"text":"    fn TestParseLnDaemon__test_invalid_line() {","highlight_start":8,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":14057,"byte_end":14093,"line_start":367,"line_end":367,"column_start":8,"column_end":44,"is_primary":true,"text":[{"text":"    fn TestParseLnDaemon__test_invalid_line() {","highlight_start":8,"highlight_end":44}],"label":null,"suggested_replacement":"test_parse_ln_daemon_test_invalid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnDaemon__test_invalid_line` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:367:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m367\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnDaemon__test_invalid_line() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_daemon_test_invalid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnDHCP__test_valid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":14490,"byte_end":14522,"line_start":384,"line_end":384,"column_start":8,"column_end":40,"is_primary":true,"text":[{"text":"    fn TestParseLnDHCP__test_valid_line() {","highlight_start":8,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":14490,"byte_end":14522,"line_start":384,"line_end":384,"column_start":8,"column_end":40,"is_primary":true,"text":[{"text":"    fn TestParseLnDHCP__test_valid_line() {","highlight_start":8,"highlight_end":40}],"label":null,"suggested_replacement":"test_parse_ln_dhcp_test_valid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnDHCP__test_valid_line` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:384:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m384\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnDHCP__test_valid_line() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_dhcp_test_valid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnDHCP__test_mapping_object_ip_to_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":16402,"byte_end":16456,"line_start":432,"line_end":432,"column_start":8,"column_end":62,"is_primary":true,"text":[{"text":"    fn TestParseLnDHCP__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":16402,"byte_end":16456,"line_start":432,"line_end":432,"column_start":8,"column_end":62,"is_primary":true,"text":[{"text":"    fn TestParseLnDHCP__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":62}],"label":null,"suggested_replacement":"test_parse_ln_dhcp_test_mapping_object_ip_to_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnDHCP__test_mapping_object_ip_to_object_name` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:432:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m432\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnDHCP__test_mapping_object_ip_to_object_name() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_dhcp_test_mapping_object_ip_to_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnDHCP__test_invalid_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":16504,"byte_end":16545,"line_start":437,"line_end":437,"column_start":8,"column_end":49,"is_primary":true,"text":[{"text":"    fn TestParseLnDHCP__test_invalid_object_name() {","highlight_start":8,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":16504,"byte_end":16545,"line_start":437,"line_end":437,"column_start":8,"column_end":49,"is_primary":true,"text":[{"text":"    fn TestParseLnDHCP__test_invalid_object_name() {","highlight_start":8,"highlight_end":49}],"label":null,"suggested_replacement":"test_parse_ln_dhcp_test_invalid_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnDHCP__test_invalid_object_name` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:437:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m437\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnDHCP__test_invalid_object_name() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_dhcp_test_invalid_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnDHCP__test_invalid_event_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":16593,"byte_end":16633,"line_start":442,"line_end":442,"column_start":8,"column_end":48,"is_primary":true,"text":[{"text":"    fn TestParseLnDHCP__test_invalid_event_type() {","highlight_start":8,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":16593,"byte_end":16633,"line_start":442,"line_end":442,"column_start":8,"column_end":48,"is_primary":true,"text":[{"text":"    fn TestParseLnDHCP__test_invalid_event_type() {","highlight_start":8,"highlight_end":48}],"label":null,"suggested_replacement":"test_parse_ln_dhcp_test_invalid_event_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnDHCP__test_invalid_event_type` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:442:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m442\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnDHCP__test_invalid_event_type() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_dhcp_test_invalid_event_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnDHCP__test_invalid_alert_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":17233,"byte_end":17273,"line_start":457,"line_end":457,"column_start":8,"column_end":48,"is_primary":true,"text":[{"text":"    fn TestParseLnDHCP__test_invalid_alert_type() {","highlight_start":8,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":17233,"byte_end":17273,"line_start":457,"line_end":457,"column_start":8,"column_end":48,"is_primary":true,"text":[{"text":"    fn TestParseLnDHCP__test_invalid_alert_type() {","highlight_start":8,"highlight_end":48}],"label":null,"suggested_replacement":"test_parse_ln_dhcp_test_invalid_alert_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnDHCP__test_invalid_alert_type` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:457:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m457\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnDHCP__test_invalid_alert_type() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_dhcp_test_invalid_alert_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnDHCP__test_invalid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":18133,"byte_end":18167,"line_start":482,"line_end":482,"column_start":8,"column_end":42,"is_primary":true,"text":[{"text":"    fn TestParseLnDHCP__test_invalid_line() {","highlight_start":8,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":18133,"byte_end":18167,"line_start":482,"line_end":482,"column_start":8,"column_end":42,"is_primary":true,"text":[{"text":"    fn TestParseLnDHCP__test_invalid_line() {","highlight_start":8,"highlight_end":42}],"label":null,"suggested_replacement":"test_parse_ln_dhcp_test_invalid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnDHCP__test_invalid_line` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:482:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m482\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnDHCP__test_invalid_line() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_dhcp_test_invalid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnDNS__test_valid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":19462,"byte_end":19493,"line_start":516,"line_end":516,"column_start":8,"column_end":39,"is_primary":true,"text":[{"text":"    fn TestParseLnDNS__test_valid_line() {","highlight_start":8,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":19462,"byte_end":19493,"line_start":516,"line_end":516,"column_start":8,"column_end":39,"is_primary":true,"text":[{"text":"    fn TestParseLnDNS__test_valid_line() {","highlight_start":8,"highlight_end":39}],"label":null,"suggested_replacement":"test_parse_ln_dns_test_valid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnDNS__test_valid_line` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:516:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m516\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnDNS__test_valid_line() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_dns_test_valid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnDNS__test_mapping_object_ip_to_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":21366,"byte_end":21419,"line_start":561,"line_end":561,"column_start":8,"column_end":61,"is_primary":true,"text":[{"text":"    fn TestParseLnDNS__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":21366,"byte_end":21419,"line_start":561,"line_end":561,"column_start":8,"column_end":61,"is_primary":true,"text":[{"text":"    fn TestParseLnDNS__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":61}],"label":null,"suggested_replacement":"test_parse_ln_dns_test_mapping_object_ip_to_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnDNS__test_mapping_object_ip_to_object_name` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:561:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m561\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnDNS__test_mapping_object_ip_to_object_name() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_dns_test_mapping_object_ip_to_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnDNS__test_invalid_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":21467,"byte_end":21507,"line_start":566,"line_end":566,"column_start":8,"column_end":48,"is_primary":true,"text":[{"text":"    fn TestParseLnDNS__test_invalid_object_name() {","highlight_start":8,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":21467,"byte_end":21507,"line_start":566,"line_end":566,"column_start":8,"column_end":48,"is_primary":true,"text":[{"text":"    fn TestParseLnDNS__test_invalid_object_name() {","highlight_start":8,"highlight_end":48}],"label":null,"suggested_replacement":"test_parse_ln_dns_test_invalid_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnDNS__test_invalid_object_name` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:566:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m566\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnDNS__test_invalid_object_name() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_dns_test_invalid_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnDNS__test_invalid_event_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":21555,"byte_end":21594,"line_start":571,"line_end":571,"column_start":8,"column_end":47,"is_primary":true,"text":[{"text":"    fn TestParseLnDNS__test_invalid_event_type() {","highlight_start":8,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":21555,"byte_end":21594,"line_start":571,"line_end":571,"column_start":8,"column_end":47,"is_primary":true,"text":[{"text":"    fn TestParseLnDNS__test_invalid_event_type() {","highlight_start":8,"highlight_end":47}],"label":null,"suggested_replacement":"test_parse_ln_dns_test_invalid_event_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnDNS__test_invalid_event_type` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:571:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m571\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnDNS__test_invalid_event_type() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_dns_test_invalid_event_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnDNS__test_invalid_alert_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":22252,"byte_end":22291,"line_start":586,"line_end":586,"column_start":8,"column_end":47,"is_primary":true,"text":[{"text":"    fn TestParseLnDNS__test_invalid_alert_type() {","highlight_start":8,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":22252,"byte_end":22291,"line_start":586,"line_end":586,"column_start":8,"column_end":47,"is_primary":true,"text":[{"text":"    fn TestParseLnDNS__test_invalid_alert_type() {","highlight_start":8,"highlight_end":47}],"label":null,"suggested_replacement":"test_parse_ln_dns_test_invalid_alert_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnDNS__test_invalid_alert_type` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:586:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m586\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnDNS__test_invalid_alert_type() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_dns_test_invalid_alert_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnDNS__test_invalid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":23209,"byte_end":23242,"line_start":611,"line_end":611,"column_start":8,"column_end":41,"is_primary":true,"text":[{"text":"    fn TestParseLnDNS__test_invalid_line() {","highlight_start":8,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":23209,"byte_end":23242,"line_start":611,"line_end":611,"column_start":8,"column_end":41,"is_primary":true,"text":[{"text":"    fn TestParseLnDNS__test_invalid_line() {","highlight_start":8,"highlight_end":41}],"label":null,"suggested_replacement":"test_parse_ln_dns_test_invalid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnDNS__test_invalid_line` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:611:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m611\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnDNS__test_invalid_line() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_dns_test_invalid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnFilterLog__test_valid_line_ipv4_tcp` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":23607,"byte_end":23653,"line_start":628,"line_end":628,"column_start":8,"column_end":54,"is_primary":true,"text":[{"text":"    fn TestParseLnFilterLog__test_valid_line_ipv4_tcp() {","highlight_start":8,"highlight_end":54}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":23607,"byte_end":23653,"line_start":628,"line_end":628,"column_start":8,"column_end":54,"is_primary":true,"text":[{"text":"    fn TestParseLnFilterLog__test_valid_line_ipv4_tcp() {","highlight_start":8,"highlight_end":54}],"label":null,"suggested_replacement":"test_parse_ln_filter_log_test_valid_line_ipv4_tcp","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnFilterLog__test_valid_line_ipv4_tcp` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:628:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m628\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnFilterLog__test_valid_line_ipv4_tcp() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_filter_log_test_valid_line_ipv4_tcp`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnFilterLog__test_valid_line_ipv6_udp` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":24789,"byte_end":24835,"line_start":658,"line_end":658,"column_start":8,"column_end":54,"is_primary":true,"text":[{"text":"    fn TestParseLnFilterLog__test_valid_line_ipv6_udp() {","highlight_start":8,"highlight_end":54}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":24789,"byte_end":24835,"line_start":658,"line_end":658,"column_start":8,"column_end":54,"is_primary":true,"text":[{"text":"    fn TestParseLnFilterLog__test_valid_line_ipv6_udp() {","highlight_start":8,"highlight_end":54}],"label":null,"suggested_replacement":"test_parse_ln_filter_log_test_valid_line_ipv6_udp","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnFilterLog__test_valid_line_ipv6_udp` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:658:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m658\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnFilterLog__test_valid_line_ipv6_udp() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_filter_log_test_valid_line_ipv6_udp`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnFilterLog__test_mapping_object_ip_to_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":25944,"byte_end":26003,"line_start":688,"line_end":688,"column_start":8,"column_end":67,"is_primary":true,"text":[{"text":"    fn TestParseLnFilterLog__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":67}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":25944,"byte_end":26003,"line_start":688,"line_end":688,"column_start":8,"column_end":67,"is_primary":true,"text":[{"text":"    fn TestParseLnFilterLog__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":67}],"label":null,"suggested_replacement":"test_parse_ln_filter_log_test_mapping_object_ip_to_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnFilterLog__test_mapping_object_ip_to_object_name` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:688:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m688\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnFilterLog__test_mapping_object_ip_to_object_name() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_filter_log_test_mapping_object_ip_to_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnFilterLog__test_invalid_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":27142,"byte_end":27188,"line_start":718,"line_end":718,"column_start":8,"column_end":54,"is_primary":true,"text":[{"text":"    fn TestParseLnFilterLog__test_invalid_object_name() {","highlight_start":8,"highlight_end":54}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":27142,"byte_end":27188,"line_start":718,"line_end":718,"column_start":8,"column_end":54,"is_primary":true,"text":[{"text":"    fn TestParseLnFilterLog__test_invalid_object_name() {","highlight_start":8,"highlight_end":54}],"label":null,"suggested_replacement":"test_parse_ln_filter_log_test_invalid_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnFilterLog__test_invalid_object_name` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:718:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m718\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnFilterLog__test_invalid_object_name() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_filter_log_test_invalid_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnFilterLog__test_invalid_event_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":27711,"byte_end":27756,"line_start":733,"line_end":733,"column_start":8,"column_end":53,"is_primary":true,"text":[{"text":"    fn TestParseLnFilterLog__test_invalid_event_type() {","highlight_start":8,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":27711,"byte_end":27756,"line_start":733,"line_end":733,"column_start":8,"column_end":53,"is_primary":true,"text":[{"text":"    fn TestParseLnFilterLog__test_invalid_event_type() {","highlight_start":8,"highlight_end":53}],"label":null,"suggested_replacement":"test_parse_ln_filter_log_test_invalid_event_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnFilterLog__test_invalid_event_type` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:733:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m733\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnFilterLog__test_invalid_event_type() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_filter_log_test_invalid_event_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnFilterLog__test_invalid_alert_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":27804,"byte_end":27849,"line_start":738,"line_end":738,"column_start":8,"column_end":53,"is_primary":true,"text":[{"text":"    fn TestParseLnFilterLog__test_invalid_alert_type() {","highlight_start":8,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":27804,"byte_end":27849,"line_start":738,"line_end":738,"column_start":8,"column_end":53,"is_primary":true,"text":[{"text":"    fn TestParseLnFilterLog__test_invalid_alert_type() {","highlight_start":8,"highlight_end":53}],"label":null,"suggested_replacement":"test_parse_ln_filter_log_test_invalid_alert_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnFilterLog__test_invalid_alert_type` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:738:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m738\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnFilterLog__test_invalid_alert_type() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_filter_log_test_invalid_alert_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnFilterLog__test_invalid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":28375,"byte_end":28414,"line_start":753,"line_end":753,"column_start":8,"column_end":47,"is_primary":true,"text":[{"text":"    fn TestParseLnFilterLog__test_invalid_line() {","highlight_start":8,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":28375,"byte_end":28414,"line_start":753,"line_end":753,"column_start":8,"column_end":47,"is_primary":true,"text":[{"text":"    fn TestParseLnFilterLog__test_invalid_line() {","highlight_start":8,"highlight_end":47}],"label":null,"suggested_replacement":"test_parse_ln_filter_log_test_invalid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnFilterLog__test_invalid_line` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:753:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m753\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnFilterLog__test_invalid_line() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_filter_log_test_invalid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnRouter__test_valid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":28816,"byte_end":28850,"line_start":770,"line_end":770,"column_start":8,"column_end":42,"is_primary":true,"text":[{"text":"    fn TestParseLnRouter__test_valid_line() {","highlight_start":8,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":28816,"byte_end":28850,"line_start":770,"line_end":770,"column_start":8,"column_end":42,"is_primary":true,"text":[{"text":"    fn TestParseLnRouter__test_valid_line() {","highlight_start":8,"highlight_end":42}],"label":null,"suggested_replacement":"test_parse_ln_router_test_valid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnRouter__test_valid_line` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:770:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m770\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnRouter__test_valid_line() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_router_test_valid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnRouter__test_mapping_object_ip_to_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":30059,"byte_end":30115,"line_start":804,"line_end":804,"column_start":8,"column_end":64,"is_primary":true,"text":[{"text":"    fn TestParseLnRouter__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":64}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":30059,"byte_end":30115,"line_start":804,"line_end":804,"column_start":8,"column_end":64,"is_primary":true,"text":[{"text":"    fn TestParseLnRouter__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":64}],"label":null,"suggested_replacement":"test_parse_ln_router_test_mapping_object_ip_to_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnRouter__test_mapping_object_ip_to_object_name` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:804:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m804\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnRouter__test_mapping_object_ip_to_object_name() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_router_test_mapping_object_ip_to_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnRouter__test_invalid_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":31327,"byte_end":31370,"line_start":838,"line_end":838,"column_start":8,"column_end":51,"is_primary":true,"text":[{"text":"    fn TestParseLnRouter__test_invalid_object_name() {","highlight_start":8,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":31327,"byte_end":31370,"line_start":838,"line_end":838,"column_start":8,"column_end":51,"is_primary":true,"text":[{"text":"    fn TestParseLnRouter__test_invalid_object_name() {","highlight_start":8,"highlight_end":51}],"label":null,"suggested_replacement":"test_parse_ln_router_test_invalid_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnRouter__test_invalid_object_name` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:838:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m838\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnRouter__test_invalid_object_name() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_router_test_invalid_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnRouter__test_invalid_event_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":32109,"byte_end":32151,"line_start":863,"line_end":863,"column_start":8,"column_end":50,"is_primary":true,"text":[{"text":"    fn TestParseLnRouter__test_invalid_event_type() {","highlight_start":8,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":32109,"byte_end":32151,"line_start":863,"line_end":863,"column_start":8,"column_end":50,"is_primary":true,"text":[{"text":"    fn TestParseLnRouter__test_invalid_event_type() {","highlight_start":8,"highlight_end":50}],"label":null,"suggested_replacement":"test_parse_ln_router_test_invalid_event_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnRouter__test_invalid_event_type` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:863:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m863\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnRouter__test_invalid_event_type() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_router_test_invalid_event_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnRouter__test_invalid_alert_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":32636,"byte_end":32678,"line_start":878,"line_end":878,"column_start":8,"column_end":50,"is_primary":true,"text":[{"text":"    fn TestParseLnRouter__test_invalid_alert_type() {","highlight_start":8,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":32636,"byte_end":32678,"line_start":878,"line_end":878,"column_start":8,"column_end":50,"is_primary":true,"text":[{"text":"    fn TestParseLnRouter__test_invalid_alert_type() {","highlight_start":8,"highlight_end":50}],"label":null,"suggested_replacement":"test_parse_ln_router_test_invalid_alert_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnRouter__test_invalid_alert_type` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:878:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m878\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnRouter__test_invalid_alert_type() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_router_test_invalid_alert_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnRouter__test_invalid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":32726,"byte_end":32762,"line_start":883,"line_end":883,"column_start":8,"column_end":44,"is_primary":true,"text":[{"text":"    fn TestParseLnRouter__test_invalid_line() {","highlight_start":8,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":32726,"byte_end":32762,"line_start":883,"line_end":883,"column_start":8,"column_end":44,"is_primary":true,"text":[{"text":"    fn TestParseLnRouter__test_invalid_line() {","highlight_start":8,"highlight_end":44}],"label":null,"suggested_replacement":"test_parse_ln_router_test_invalid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnRouter__test_invalid_line` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:883:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m883\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnRouter__test_invalid_line() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_router_test_invalid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnRouterBoard__test_valid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":33166,"byte_end":33205,"line_start":900,"line_end":900,"column_start":8,"column_end":47,"is_primary":true,"text":[{"text":"    fn TestParseLnRouterBoard__test_valid_line() {","highlight_start":8,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":33166,"byte_end":33205,"line_start":900,"line_end":900,"column_start":8,"column_end":47,"is_primary":true,"text":[{"text":"    fn TestParseLnRouterBoard__test_valid_line() {","highlight_start":8,"highlight_end":47}],"label":null,"suggested_replacement":"test_parse_ln_router_board_test_valid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnRouterBoard__test_valid_line` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:900:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m900\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnRouterBoard__test_valid_line() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_router_board_test_valid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnRouterBoard__test_mapping_object_ip_to_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":34364,"byte_end":34425,"line_start":934,"line_end":934,"column_start":8,"column_end":69,"is_primary":true,"text":[{"text":"    fn TestParseLnRouterBoard__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":34364,"byte_end":34425,"line_start":934,"line_end":934,"column_start":8,"column_end":69,"is_primary":true,"text":[{"text":"    fn TestParseLnRouterBoard__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":69}],"label":null,"suggested_replacement":"test_parse_ln_router_board_test_mapping_object_ip_to_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnRouterBoard__test_mapping_object_ip_to_object_name` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:934:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m934\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnRouterBoard__test_mapping_object_ip_to_object_name() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_router_board_test_mapping_object_ip_to_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnRouterBoard__test_invalid_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":35582,"byte_end":35630,"line_start":968,"line_end":968,"column_start":8,"column_end":56,"is_primary":true,"text":[{"text":"    fn TestParseLnRouterBoard__test_invalid_object_name() {","highlight_start":8,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":35582,"byte_end":35630,"line_start":968,"line_end":968,"column_start":8,"column_end":56,"is_primary":true,"text":[{"text":"    fn TestParseLnRouterBoard__test_invalid_object_name() {","highlight_start":8,"highlight_end":56}],"label":null,"suggested_replacement":"test_parse_ln_router_board_test_invalid_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnRouterBoard__test_invalid_object_name` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:968:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m968\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnRouterBoard__test_invalid_object_name() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_router_board_test_invalid_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnRouterBoard__test_invalid_event_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":36354,"byte_end":36401,"line_start":993,"line_end":993,"column_start":8,"column_end":55,"is_primary":true,"text":[{"text":"    fn TestParseLnRouterBoard__test_invalid_event_type() {","highlight_start":8,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":36354,"byte_end":36401,"line_start":993,"line_end":993,"column_start":8,"column_end":55,"is_primary":true,"text":[{"text":"    fn TestParseLnRouterBoard__test_invalid_event_type() {","highlight_start":8,"highlight_end":55}],"label":null,"suggested_replacement":"test_parse_ln_router_board_test_invalid_event_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnRouterBoard__test_invalid_event_type` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:993:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m993\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnRouterBoard__test_invalid_event_type() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_router_board_test_invalid_event_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnRouterBoard__test_invalid_alert_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":36866,"byte_end":36913,"line_start":1008,"line_end":1008,"column_start":8,"column_end":55,"is_primary":true,"text":[{"text":"    fn TestParseLnRouterBoard__test_invalid_alert_type() {","highlight_start":8,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":36866,"byte_end":36913,"line_start":1008,"line_end":1008,"column_start":8,"column_end":55,"is_primary":true,"text":[{"text":"    fn TestParseLnRouterBoard__test_invalid_alert_type() {","highlight_start":8,"highlight_end":55}],"label":null,"suggested_replacement":"test_parse_ln_router_board_test_invalid_alert_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnRouterBoard__test_invalid_alert_type` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1008:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1008\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnRouterBoard__test_invalid_alert_type() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_router_board_test_invalid_alert_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnRouterBoard__test_invalid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":36961,"byte_end":37002,"line_start":1013,"line_end":1013,"column_start":8,"column_end":49,"is_primary":true,"text":[{"text":"    fn TestParseLnRouterBoard__test_invalid_line() {","highlight_start":8,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":36961,"byte_end":37002,"line_start":1013,"line_end":1013,"column_start":8,"column_end":49,"is_primary":true,"text":[{"text":"    fn TestParseLnRouterBoard__test_invalid_line() {","highlight_start":8,"highlight_end":49}],"label":null,"suggested_replacement":"test_parse_ln_router_board_test_invalid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnRouterBoard__test_invalid_line` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1013:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1013\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnRouterBoard__test_invalid_line() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_router_board_test_invalid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnSnort__test_valid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":37415,"byte_end":37448,"line_start":1030,"line_end":1030,"column_start":8,"column_end":41,"is_primary":true,"text":[{"text":"    fn TestParseLnSnort__test_valid_line() {","highlight_start":8,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":37415,"byte_end":37448,"line_start":1030,"line_end":1030,"column_start":8,"column_end":41,"is_primary":true,"text":[{"text":"    fn TestParseLnSnort__test_valid_line() {","highlight_start":8,"highlight_end":41}],"label":null,"suggested_replacement":"test_parse_ln_snort_test_valid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnSnort__test_valid_line` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1030:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1030\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnSnort__test_valid_line() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_snort_test_valid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnSnort__test_mapping_object_ip_to_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":38883,"byte_end":38938,"line_start":1070,"line_end":1070,"column_start":8,"column_end":63,"is_primary":true,"text":[{"text":"    fn TestParseLnSnort__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":38883,"byte_end":38938,"line_start":1070,"line_end":1070,"column_start":8,"column_end":63,"is_primary":true,"text":[{"text":"    fn TestParseLnSnort__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":63}],"label":null,"suggested_replacement":"test_parse_ln_snort_test_mapping_object_ip_to_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnSnort__test_mapping_object_ip_to_object_name` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1070:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1070\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnSnort__test_mapping_object_ip_to_object_name() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_snort_test_mapping_object_ip_to_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnSnort__test_invalid_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":40376,"byte_end":40418,"line_start":1110,"line_end":1110,"column_start":8,"column_end":50,"is_primary":true,"text":[{"text":"    fn TestParseLnSnort__test_invalid_object_name() {","highlight_start":8,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":40376,"byte_end":40418,"line_start":1110,"line_end":1110,"column_start":8,"column_end":50,"is_primary":true,"text":[{"text":"    fn TestParseLnSnort__test_invalid_object_name() {","highlight_start":8,"highlight_end":50}],"label":null,"suggested_replacement":"test_parse_ln_snort_test_invalid_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnSnort__test_invalid_object_name` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1110:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnSnort__test_invalid_object_name() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_snort_test_invalid_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnSnort__test_invalid_event_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":41180,"byte_end":41221,"line_start":1135,"line_end":1135,"column_start":8,"column_end":49,"is_primary":true,"text":[{"text":"    fn TestParseLnSnort__test_invalid_event_type() {","highlight_start":8,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":41180,"byte_end":41221,"line_start":1135,"line_end":1135,"column_start":8,"column_end":49,"is_primary":true,"text":[{"text":"    fn TestParseLnSnort__test_invalid_event_type() {","highlight_start":8,"highlight_end":49}],"label":null,"suggested_replacement":"test_parse_ln_snort_test_invalid_event_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnSnort__test_invalid_event_type` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1135:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1135\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnSnort__test_invalid_event_type() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_snort_test_invalid_event_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnSnort__test_invalid_alert_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":41728,"byte_end":41769,"line_start":1150,"line_end":1150,"column_start":8,"column_end":49,"is_primary":true,"text":[{"text":"    fn TestParseLnSnort__test_invalid_alert_type() {","highlight_start":8,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":41728,"byte_end":41769,"line_start":1150,"line_end":1150,"column_start":8,"column_end":49,"is_primary":true,"text":[{"text":"    fn TestParseLnSnort__test_invalid_alert_type() {","highlight_start":8,"highlight_end":49}],"label":null,"suggested_replacement":"test_parse_ln_snort_test_invalid_alert_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnSnort__test_invalid_alert_type` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1150:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1150\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnSnort__test_invalid_alert_type() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_snort_test_invalid_alert_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnSnort__test_invalid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":42538,"byte_end":42573,"line_start":1175,"line_end":1175,"column_start":8,"column_end":43,"is_primary":true,"text":[{"text":"    fn TestParseLnSnort__test_invalid_line() {","highlight_start":8,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":42538,"byte_end":42573,"line_start":1175,"line_end":1175,"column_start":8,"column_end":43,"is_primary":true,"text":[{"text":"    fn TestParseLnSnort__test_invalid_line() {","highlight_start":8,"highlight_end":43}],"label":null,"suggested_replacement":"test_parse_ln_snort_test_invalid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnSnort__test_invalid_line` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1175:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1175\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnSnort__test_invalid_line() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_snort_test_invalid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnSquid__test_valid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":42970,"byte_end":43003,"line_start":1192,"line_end":1192,"column_start":8,"column_end":41,"is_primary":true,"text":[{"text":"    fn TestParseLnSquid__test_valid_line() {","highlight_start":8,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":42970,"byte_end":43003,"line_start":1192,"line_end":1192,"column_start":8,"column_end":41,"is_primary":true,"text":[{"text":"    fn TestParseLnSquid__test_valid_line() {","highlight_start":8,"highlight_end":41}],"label":null,"suggested_replacement":"test_parse_ln_squid_test_valid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnSquid__test_valid_line` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1192:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1192\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnSquid__test_valid_line() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_squid_test_valid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnSquid__test_mapping_object_ip_to_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":44184,"byte_end":44239,"line_start":1223,"line_end":1223,"column_start":8,"column_end":63,"is_primary":true,"text":[{"text":"    fn TestParseLnSquid__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":44184,"byte_end":44239,"line_start":1223,"line_end":1223,"column_start":8,"column_end":63,"is_primary":true,"text":[{"text":"    fn TestParseLnSquid__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":63}],"label":null,"suggested_replacement":"test_parse_ln_squid_test_mapping_object_ip_to_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnSquid__test_mapping_object_ip_to_object_name` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1223:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1223\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnSquid__test_mapping_object_ip_to_object_name() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_squid_test_mapping_object_ip_to_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnSquid__test_invalid_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":45423,"byte_end":45465,"line_start":1254,"line_end":1254,"column_start":8,"column_end":50,"is_primary":true,"text":[{"text":"    fn TestParseLnSquid__test_invalid_object_name() {","highlight_start":8,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":45423,"byte_end":45465,"line_start":1254,"line_end":1254,"column_start":8,"column_end":50,"is_primary":true,"text":[{"text":"    fn TestParseLnSquid__test_invalid_object_name() {","highlight_start":8,"highlight_end":50}],"label":null,"suggested_replacement":"test_parse_ln_squid_test_invalid_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnSquid__test_invalid_object_name` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1254:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1254\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnSquid__test_invalid_object_name() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_squid_test_invalid_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnSquid__test_invalid_event_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":45964,"byte_end":46005,"line_start":1269,"line_end":1269,"column_start":8,"column_end":49,"is_primary":true,"text":[{"text":"    fn TestParseLnSquid__test_invalid_event_type() {","highlight_start":8,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":45964,"byte_end":46005,"line_start":1269,"line_end":1269,"column_start":8,"column_end":49,"is_primary":true,"text":[{"text":"    fn TestParseLnSquid__test_invalid_event_type() {","highlight_start":8,"highlight_end":49}],"label":null,"suggested_replacement":"test_parse_ln_squid_test_invalid_event_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnSquid__test_invalid_event_type` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1269:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1269\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnSquid__test_invalid_event_type() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_squid_test_invalid_event_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnSquid__test_invalid_alert_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":46053,"byte_end":46094,"line_start":1274,"line_end":1274,"column_start":8,"column_end":49,"is_primary":true,"text":[{"text":"    fn TestParseLnSquid__test_invalid_alert_type() {","highlight_start":8,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":46053,"byte_end":46094,"line_start":1274,"line_end":1274,"column_start":8,"column_end":49,"is_primary":true,"text":[{"text":"    fn TestParseLnSquid__test_invalid_alert_type() {","highlight_start":8,"highlight_end":49}],"label":null,"suggested_replacement":"test_parse_ln_squid_test_invalid_alert_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnSquid__test_invalid_alert_type` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1274:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1274\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnSquid__test_invalid_alert_type() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_squid_test_invalid_alert_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnSquid__test_invalid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":46596,"byte_end":46631,"line_start":1289,"line_end":1289,"column_start":8,"column_end":43,"is_primary":true,"text":[{"text":"    fn TestParseLnSquid__test_invalid_line() {","highlight_start":8,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":46596,"byte_end":46631,"line_start":1289,"line_end":1289,"column_start":8,"column_end":43,"is_primary":true,"text":[{"text":"    fn TestParseLnSquid__test_invalid_line() {","highlight_start":8,"highlight_end":43}],"label":null,"suggested_replacement":"test_parse_ln_squid_test_invalid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnSquid__test_invalid_line` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1289:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1289\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnSquid__test_invalid_line() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_squid_test_invalid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnSwitch__test_valid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":47029,"byte_end":47063,"line_start":1306,"line_end":1306,"column_start":8,"column_end":42,"is_primary":true,"text":[{"text":"    fn TestParseLnSwitch__test_valid_line() {","highlight_start":8,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":47029,"byte_end":47063,"line_start":1306,"line_end":1306,"column_start":8,"column_end":42,"is_primary":true,"text":[{"text":"    fn TestParseLnSwitch__test_valid_line() {","highlight_start":8,"highlight_end":42}],"label":null,"suggested_replacement":"test_parse_ln_switch_test_valid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnSwitch__test_valid_line` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1306:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1306\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnSwitch__test_valid_line() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_switch_test_valid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnSwitch__test_mapping_object_ip_to_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":48338,"byte_end":48394,"line_start":1340,"line_end":1340,"column_start":8,"column_end":64,"is_primary":true,"text":[{"text":"    fn TestParseLnSwitch__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":64}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":48338,"byte_end":48394,"line_start":1340,"line_end":1340,"column_start":8,"column_end":64,"is_primary":true,"text":[{"text":"    fn TestParseLnSwitch__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":64}],"label":null,"suggested_replacement":"test_parse_ln_switch_test_mapping_object_ip_to_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnSwitch__test_mapping_object_ip_to_object_name` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1340:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1340\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnSwitch__test_mapping_object_ip_to_object_name() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_switch_test_mapping_object_ip_to_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnSwitch__test_invalid_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":49672,"byte_end":49715,"line_start":1374,"line_end":1374,"column_start":8,"column_end":51,"is_primary":true,"text":[{"text":"    fn TestParseLnSwitch__test_invalid_object_name() {","highlight_start":8,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":49672,"byte_end":49715,"line_start":1374,"line_end":1374,"column_start":8,"column_end":51,"is_primary":true,"text":[{"text":"    fn TestParseLnSwitch__test_invalid_object_name() {","highlight_start":8,"highlight_end":51}],"label":null,"suggested_replacement":"test_parse_ln_switch_test_invalid_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnSwitch__test_invalid_object_name` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1374:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1374\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnSwitch__test_invalid_object_name() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_switch_test_invalid_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnSwitch__test_invalid_event_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":50487,"byte_end":50529,"line_start":1399,"line_end":1399,"column_start":8,"column_end":50,"is_primary":true,"text":[{"text":"    fn TestParseLnSwitch__test_invalid_event_type() {","highlight_start":8,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":50487,"byte_end":50529,"line_start":1399,"line_end":1399,"column_start":8,"column_end":50,"is_primary":true,"text":[{"text":"    fn TestParseLnSwitch__test_invalid_event_type() {","highlight_start":8,"highlight_end":50}],"label":null,"suggested_replacement":"test_parse_ln_switch_test_invalid_event_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnSwitch__test_invalid_event_type` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1399:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1399\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnSwitch__test_invalid_event_type() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_switch_test_invalid_event_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnSwitch__test_invalid_alert_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":51047,"byte_end":51089,"line_start":1414,"line_end":1414,"column_start":8,"column_end":50,"is_primary":true,"text":[{"text":"    fn TestParseLnSwitch__test_invalid_alert_type() {","highlight_start":8,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":51047,"byte_end":51089,"line_start":1414,"line_end":1414,"column_start":8,"column_end":50,"is_primary":true,"text":[{"text":"    fn TestParseLnSwitch__test_invalid_alert_type() {","highlight_start":8,"highlight_end":50}],"label":null,"suggested_replacement":"test_parse_ln_switch_test_invalid_alert_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnSwitch__test_invalid_alert_type` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1414:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1414\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnSwitch__test_invalid_alert_type() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_switch_test_invalid_alert_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnSwitch__test_invalid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":51137,"byte_end":51173,"line_start":1419,"line_end":1419,"column_start":8,"column_end":44,"is_primary":true,"text":[{"text":"    fn TestParseLnSwitch__test_invalid_line() {","highlight_start":8,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":51137,"byte_end":51173,"line_start":1419,"line_end":1419,"column_start":8,"column_end":44,"is_primary":true,"text":[{"text":"    fn TestParseLnSwitch__test_invalid_line() {","highlight_start":8,"highlight_end":44}],"label":null,"suggested_replacement":"test_parse_ln_switch_test_invalid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnSwitch__test_invalid_line` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1419:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1419\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnSwitch__test_invalid_line() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_switch_test_invalid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnUserAudit__test_valid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":51575,"byte_end":51612,"line_start":1436,"line_end":1436,"column_start":8,"column_end":45,"is_primary":true,"text":[{"text":"    fn TestParseLnUserAudit__test_valid_line() {","highlight_start":8,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":51575,"byte_end":51612,"line_start":1436,"line_end":1436,"column_start":8,"column_end":45,"is_primary":true,"text":[{"text":"    fn TestParseLnUserAudit__test_valid_line() {","highlight_start":8,"highlight_end":45}],"label":null,"suggested_replacement":"test_parse_ln_user_audit_test_valid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnUserAudit__test_valid_line` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1436:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1436\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnUserAudit__test_valid_line() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_user_audit_test_valid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnUserAudit__test_mapping_object_ip_to_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":53025,"byte_end":53084,"line_start":1477,"line_end":1477,"column_start":8,"column_end":67,"is_primary":true,"text":[{"text":"    fn TestParseLnUserAudit__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":67}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":53025,"byte_end":53084,"line_start":1477,"line_end":1477,"column_start":8,"column_end":67,"is_primary":true,"text":[{"text":"    fn TestParseLnUserAudit__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":67}],"label":null,"suggested_replacement":"test_parse_ln_user_audit_test_mapping_object_ip_to_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnUserAudit__test_mapping_object_ip_to_object_name` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1477:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1477\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnUserAudit__test_mapping_object_ip_to_object_name() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_user_audit_test_mapping_object_ip_to_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnUserAudit__test_invalid_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":54478,"byte_end":54524,"line_start":1517,"line_end":1517,"column_start":8,"column_end":54,"is_primary":true,"text":[{"text":"    fn TestParseLnUserAudit__test_invalid_object_name() {","highlight_start":8,"highlight_end":54}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":54478,"byte_end":54524,"line_start":1517,"line_end":1517,"column_start":8,"column_end":54,"is_primary":true,"text":[{"text":"    fn TestParseLnUserAudit__test_invalid_object_name() {","highlight_start":8,"highlight_end":54}],"label":null,"suggested_replacement":"test_parse_ln_user_audit_test_invalid_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnUserAudit__test_invalid_object_name` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1517:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1517\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnUserAudit__test_invalid_object_name() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_user_audit_test_invalid_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnUserAudit__test_invalid_event_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":55261,"byte_end":55306,"line_start":1542,"line_end":1542,"column_start":8,"column_end":53,"is_primary":true,"text":[{"text":"    fn TestParseLnUserAudit__test_invalid_event_type() {","highlight_start":8,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":55261,"byte_end":55306,"line_start":1542,"line_end":1542,"column_start":8,"column_end":53,"is_primary":true,"text":[{"text":"    fn TestParseLnUserAudit__test_invalid_event_type() {","highlight_start":8,"highlight_end":53}],"label":null,"suggested_replacement":"test_parse_ln_user_audit_test_invalid_event_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnUserAudit__test_invalid_event_type` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1542:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1542\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnUserAudit__test_invalid_event_type() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_user_audit_test_invalid_event_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnUserAudit__test_invalid_alert_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":55786,"byte_end":55831,"line_start":1557,"line_end":1557,"column_start":8,"column_end":53,"is_primary":true,"text":[{"text":"    fn TestParseLnUserAudit__test_invalid_alert_type() {","highlight_start":8,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":55786,"byte_end":55831,"line_start":1557,"line_end":1557,"column_start":8,"column_end":53,"is_primary":true,"text":[{"text":"    fn TestParseLnUserAudit__test_invalid_alert_type() {","highlight_start":8,"highlight_end":53}],"label":null,"suggested_replacement":"test_parse_ln_user_audit_test_invalid_alert_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnUserAudit__test_invalid_alert_type` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1557:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1557\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnUserAudit__test_invalid_alert_type() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_user_audit_test_invalid_alert_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnUserAudit__test_invalid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":55879,"byte_end":55918,"line_start":1562,"line_end":1562,"column_start":8,"column_end":47,"is_primary":true,"text":[{"text":"    fn TestParseLnUserAudit__test_invalid_line() {","highlight_start":8,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":55879,"byte_end":55918,"line_start":1562,"line_end":1562,"column_start":8,"column_end":47,"is_primary":true,"text":[{"text":"    fn TestParseLnUserAudit__test_invalid_line() {","highlight_start":8,"highlight_end":47}],"label":null,"suggested_replacement":"test_parse_ln_user_audit_test_invalid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnUserAudit__test_invalid_line` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1562:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1562\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnUserAudit__test_invalid_line() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_user_audit_test_invalid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnUserNotice__test_valid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":57525,"byte_end":57563,"line_start":1608,"line_end":1608,"column_start":8,"column_end":46,"is_primary":true,"text":[{"text":"    fn TestParseLnUserNotice__test_valid_line() {","highlight_start":8,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":57525,"byte_end":57563,"line_start":1608,"line_end":1608,"column_start":8,"column_end":46,"is_primary":true,"text":[{"text":"    fn TestParseLnUserNotice__test_valid_line() {","highlight_start":8,"highlight_end":46}],"label":null,"suggested_replacement":"test_parse_ln_user_notice_test_valid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnUserNotice__test_valid_line` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1608:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1608\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnUserNotice__test_valid_line() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_user_notice_test_valid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnUserNotice__test_mapping_object_ip_to_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":58774,"byte_end":58834,"line_start":1644,"line_end":1644,"column_start":8,"column_end":68,"is_primary":true,"text":[{"text":"    fn TestParseLnUserNotice__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":58774,"byte_end":58834,"line_start":1644,"line_end":1644,"column_start":8,"column_end":68,"is_primary":true,"text":[{"text":"    fn TestParseLnUserNotice__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":68}],"label":null,"suggested_replacement":"test_parse_ln_user_notice_test_mapping_object_ip_to_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnUserNotice__test_mapping_object_ip_to_object_name` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1644:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1644\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnUserNotice__test_mapping_object_ip_to_object_name() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_user_notice_test_mapping_object_ip_to_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnUserNotice__test_invalid_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":60048,"byte_end":60095,"line_start":1680,"line_end":1680,"column_start":8,"column_end":55,"is_primary":true,"text":[{"text":"    fn TestParseLnUserNotice__test_invalid_object_name() {","highlight_start":8,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":60048,"byte_end":60095,"line_start":1680,"line_end":1680,"column_start":8,"column_end":55,"is_primary":true,"text":[{"text":"    fn TestParseLnUserNotice__test_invalid_object_name() {","highlight_start":8,"highlight_end":55}],"label":null,"suggested_replacement":"test_parse_ln_user_notice_test_invalid_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnUserNotice__test_invalid_object_name` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1680:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1680\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnUserNotice__test_invalid_object_name() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_user_notice_test_invalid_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnUserNotice__test_invalid_event_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":60831,"byte_end":60877,"line_start":1705,"line_end":1705,"column_start":8,"column_end":54,"is_primary":true,"text":[{"text":"    fn TestParseLnUserNotice__test_invalid_event_type() {","highlight_start":8,"highlight_end":54}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":60831,"byte_end":60877,"line_start":1705,"line_end":1705,"column_start":8,"column_end":54,"is_primary":true,"text":[{"text":"    fn TestParseLnUserNotice__test_invalid_event_type() {","highlight_start":8,"highlight_end":54}],"label":null,"suggested_replacement":"test_parse_ln_user_notice_test_invalid_event_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnUserNotice__test_invalid_event_type` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1705:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1705\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnUserNotice__test_invalid_event_type() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_user_notice_test_invalid_event_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnUserNotice__test_invalid_alert_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":61355,"byte_end":61401,"line_start":1720,"line_end":1720,"column_start":8,"column_end":54,"is_primary":true,"text":[{"text":"    fn TestParseLnUserNotice__test_invalid_alert_type() {","highlight_start":8,"highlight_end":54}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":61355,"byte_end":61401,"line_start":1720,"line_end":1720,"column_start":8,"column_end":54,"is_primary":true,"text":[{"text":"    fn TestParseLnUserNotice__test_invalid_alert_type() {","highlight_start":8,"highlight_end":54}],"label":null,"suggested_replacement":"test_parse_ln_user_notice_test_invalid_alert_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnUserNotice__test_invalid_alert_type` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1720:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1720\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnUserNotice__test_invalid_alert_type() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_user_notice_test_invalid_alert_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnUserNotice__test_invalid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":62142,"byte_end":62182,"line_start":1745,"line_end":1745,"column_start":8,"column_end":48,"is_primary":true,"text":[{"text":"    fn TestParseLnUserNotice__test_invalid_line() {","highlight_start":8,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":62142,"byte_end":62182,"line_start":1745,"line_end":1745,"column_start":8,"column_end":48,"is_primary":true,"text":[{"text":"    fn TestParseLnUserNotice__test_invalid_line() {","highlight_start":8,"highlight_end":48}],"label":null,"suggested_replacement":"test_parse_ln_user_notice_test_invalid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnUserNotice__test_invalid_line` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1745:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1745\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnUserNotice__test_invalid_line() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_user_notice_test_invalid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnUserWarning__test_valid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":62590,"byte_end":62629,"line_start":1762,"line_end":1762,"column_start":8,"column_end":47,"is_primary":true,"text":[{"text":"    fn TestParseLnUserWarning__test_valid_line() {","highlight_start":8,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":62590,"byte_end":62629,"line_start":1762,"line_end":1762,"column_start":8,"column_end":47,"is_primary":true,"text":[{"text":"    fn TestParseLnUserWarning__test_valid_line() {","highlight_start":8,"highlight_end":47}],"label":null,"suggested_replacement":"test_parse_ln_user_warning_test_valid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnUserWarning__test_valid_line` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1762:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1762\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnUserWarning__test_valid_line() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_user_warning_test_valid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnUserWarning__test_mapping_object_ip_to_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":63754,"byte_end":63815,"line_start":1795,"line_end":1795,"column_start":8,"column_end":69,"is_primary":true,"text":[{"text":"    fn TestParseLnUserWarning__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":63754,"byte_end":63815,"line_start":1795,"line_end":1795,"column_start":8,"column_end":69,"is_primary":true,"text":[{"text":"    fn TestParseLnUserWarning__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":69}],"label":null,"suggested_replacement":"test_parse_ln_user_warning_test_mapping_object_ip_to_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnUserWarning__test_mapping_object_ip_to_object_name` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1795:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1795\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnUserWarning__test_mapping_object_ip_to_object_name() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_user_warning_test_mapping_object_ip_to_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnUserWarning__test_invalid_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":64943,"byte_end":64991,"line_start":1828,"line_end":1828,"column_start":8,"column_end":56,"is_primary":true,"text":[{"text":"    fn TestParseLnUserWarning__test_invalid_object_name() {","highlight_start":8,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":64943,"byte_end":64991,"line_start":1828,"line_end":1828,"column_start":8,"column_end":56,"is_primary":true,"text":[{"text":"    fn TestParseLnUserWarning__test_invalid_object_name() {","highlight_start":8,"highlight_end":56}],"label":null,"suggested_replacement":"test_parse_ln_user_warning_test_invalid_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnUserWarning__test_invalid_object_name` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1828:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1828\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnUserWarning__test_invalid_object_name() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_user_warning_test_invalid_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnUserWarning__test_invalid_event_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":65719,"byte_end":65766,"line_start":1853,"line_end":1853,"column_start":8,"column_end":55,"is_primary":true,"text":[{"text":"    fn TestParseLnUserWarning__test_invalid_event_type() {","highlight_start":8,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":65719,"byte_end":65766,"line_start":1853,"line_end":1853,"column_start":8,"column_end":55,"is_primary":true,"text":[{"text":"    fn TestParseLnUserWarning__test_invalid_event_type() {","highlight_start":8,"highlight_end":55}],"label":null,"suggested_replacement":"test_parse_ln_user_warning_test_invalid_event_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnUserWarning__test_invalid_event_type` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1853:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1853\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnUserWarning__test_invalid_event_type() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_user_warning_test_invalid_event_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnUserWarning__test_invalid_alert_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":66235,"byte_end":66282,"line_start":1868,"line_end":1868,"column_start":8,"column_end":55,"is_primary":true,"text":[{"text":"    fn TestParseLnUserWarning__test_invalid_alert_type() {","highlight_start":8,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":66235,"byte_end":66282,"line_start":1868,"line_end":1868,"column_start":8,"column_end":55,"is_primary":true,"text":[{"text":"    fn TestParseLnUserWarning__test_invalid_alert_type() {","highlight_start":8,"highlight_end":55}],"label":null,"suggested_replacement":"test_parse_ln_user_warning_test_invalid_alert_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnUserWarning__test_invalid_alert_type` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1868:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1868\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnUserWarning__test_invalid_alert_type() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_user_warning_test_invalid_alert_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnUserWarning__test_invalid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":66330,"byte_end":66371,"line_start":1873,"line_end":1873,"column_start":8,"column_end":49,"is_primary":true,"text":[{"text":"    fn TestParseLnUserWarning__test_invalid_line() {","highlight_start":8,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":66330,"byte_end":66371,"line_start":1873,"line_end":1873,"column_start":8,"column_end":49,"is_primary":true,"text":[{"text":"    fn TestParseLnUserWarning__test_invalid_line() {","highlight_start":8,"highlight_end":49}],"label":null,"suggested_replacement":"test_parse_ln_user_warning_test_invalid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnUserWarning__test_invalid_line` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1873:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1873\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnUserWarning__test_invalid_line() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_user_warning_test_invalid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnVPNServer__test_valid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":66778,"byte_end":66815,"line_start":1890,"line_end":1890,"column_start":8,"column_end":45,"is_primary":true,"text":[{"text":"    fn TestParseLnVPNServer__test_valid_line() {","highlight_start":8,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":66778,"byte_end":66815,"line_start":1890,"line_end":1890,"column_start":8,"column_end":45,"is_primary":true,"text":[{"text":"    fn TestParseLnVPNServer__test_valid_line() {","highlight_start":8,"highlight_end":45}],"label":null,"suggested_replacement":"test_parse_ln_vpnserver_test_valid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnVPNServer__test_valid_line` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1890:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1890\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnVPNServer__test_valid_line() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_vpnserver_test_valid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnVPNServer__test_mapping_object_ip_to_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":68191,"byte_end":68250,"line_start":1916,"line_end":1916,"column_start":8,"column_end":67,"is_primary":true,"text":[{"text":"    fn TestParseLnVPNServer__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":67}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":68191,"byte_end":68250,"line_start":1916,"line_end":1916,"column_start":8,"column_end":67,"is_primary":true,"text":[{"text":"    fn TestParseLnVPNServer__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":67}],"label":null,"suggested_replacement":"test_parse_ln_vpnserver_test_mapping_object_ip_to_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnVPNServer__test_mapping_object_ip_to_object_name` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1916:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1916\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnVPNServer__test_mapping_object_ip_to_object_name() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_vpnserver_test_mapping_object_ip_to_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnVPNServer__test_invalid_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":68298,"byte_end":68344,"line_start":1921,"line_end":1921,"column_start":8,"column_end":54,"is_primary":true,"text":[{"text":"    fn TestParseLnVPNServer__test_invalid_object_name() {","highlight_start":8,"highlight_end":54}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":68298,"byte_end":68344,"line_start":1921,"line_end":1921,"column_start":8,"column_end":54,"is_primary":true,"text":[{"text":"    fn TestParseLnVPNServer__test_invalid_object_name() {","highlight_start":8,"highlight_end":54}],"label":null,"suggested_replacement":"test_parse_ln_vpnserver_test_invalid_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnVPNServer__test_invalid_object_name` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1921:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1921\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnVPNServer__test_invalid_object_name() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_vpnserver_test_invalid_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnVPNServer__test_invalid_event_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":68392,"byte_end":68437,"line_start":1926,"line_end":1926,"column_start":8,"column_end":53,"is_primary":true,"text":[{"text":"    fn TestParseLnVPNServer__test_invalid_event_type() {","highlight_start":8,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":68392,"byte_end":68437,"line_start":1926,"line_end":1926,"column_start":8,"column_end":53,"is_primary":true,"text":[{"text":"    fn TestParseLnVPNServer__test_invalid_event_type() {","highlight_start":8,"highlight_end":53}],"label":null,"suggested_replacement":"test_parse_ln_vpnserver_test_invalid_event_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnVPNServer__test_invalid_event_type` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1926:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1926\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnVPNServer__test_invalid_event_type() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_vpnserver_test_invalid_event_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnVPNServer__test_invalid_alert_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":68485,"byte_end":68530,"line_start":1931,"line_end":1931,"column_start":8,"column_end":53,"is_primary":true,"text":[{"text":"    fn TestParseLnVPNServer__test_invalid_alert_type() {","highlight_start":8,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":68485,"byte_end":68530,"line_start":1931,"line_end":1931,"column_start":8,"column_end":53,"is_primary":true,"text":[{"text":"    fn TestParseLnVPNServer__test_invalid_alert_type() {","highlight_start":8,"highlight_end":53}],"label":null,"suggested_replacement":"test_parse_ln_vpnserver_test_invalid_alert_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnVPNServer__test_invalid_alert_type` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1931:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1931\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnVPNServer__test_invalid_alert_type() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_vpnserver_test_invalid_alert_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnVPNServer__test_invalid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":68578,"byte_end":68617,"line_start":1936,"line_end":1936,"column_start":8,"column_end":47,"is_primary":true,"text":[{"text":"    fn TestParseLnVPNServer__test_invalid_line() {","highlight_start":8,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":68578,"byte_end":68617,"line_start":1936,"line_end":1936,"column_start":8,"column_end":47,"is_primary":true,"text":[{"text":"    fn TestParseLnVPNServer__test_invalid_line() {","highlight_start":8,"highlight_end":47}],"label":null,"suggested_replacement":"test_parse_ln_vpnserver_test_invalid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnVPNServer__test_invalid_line` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1936:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1936\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnVPNServer__test_invalid_line() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_vpnserver_test_invalid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnVMware__test_valid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":68985,"byte_end":69019,"line_start":1953,"line_end":1953,"column_start":8,"column_end":42,"is_primary":true,"text":[{"text":"    fn TestParseLnVMware__test_valid_line() {","highlight_start":8,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":68985,"byte_end":69019,"line_start":1953,"line_end":1953,"column_start":8,"column_end":42,"is_primary":true,"text":[{"text":"    fn TestParseLnVMware__test_valid_line() {","highlight_start":8,"highlight_end":42}],"label":null,"suggested_replacement":"test_parse_ln_vmware_test_valid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnVMware__test_valid_line` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1953:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1953\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnVMware__test_valid_line() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_vmware_test_valid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnVMware__test_mapping_object_ip_to_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":69952,"byte_end":70008,"line_start":1976,"line_end":1976,"column_start":8,"column_end":64,"is_primary":true,"text":[{"text":"    fn TestParseLnVMware__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":64}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":69952,"byte_end":70008,"line_start":1976,"line_end":1976,"column_start":8,"column_end":64,"is_primary":true,"text":[{"text":"    fn TestParseLnVMware__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":64}],"label":null,"suggested_replacement":"test_parse_ln_vmware_test_mapping_object_ip_to_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnVMware__test_mapping_object_ip_to_object_name` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1976:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1976\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnVMware__test_mapping_object_ip_to_object_name() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_vmware_test_mapping_object_ip_to_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnVMware__test_invalid_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":70944,"byte_end":70987,"line_start":1999,"line_end":1999,"column_start":8,"column_end":51,"is_primary":true,"text":[{"text":"    fn TestParseLnVMware__test_invalid_object_name() {","highlight_start":8,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":70944,"byte_end":70987,"line_start":1999,"line_end":1999,"column_start":8,"column_end":51,"is_primary":true,"text":[{"text":"    fn TestParseLnVMware__test_invalid_object_name() {","highlight_start":8,"highlight_end":51}],"label":null,"suggested_replacement":"test_parse_ln_vmware_test_invalid_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnVMware__test_invalid_object_name` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:1999:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1999\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnVMware__test_invalid_object_name() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_vmware_test_invalid_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnVMware__test_invalid_event_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":71492,"byte_end":71534,"line_start":2014,"line_end":2014,"column_start":8,"column_end":50,"is_primary":true,"text":[{"text":"    fn TestParseLnVMware__test_invalid_event_type() {","highlight_start":8,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":71492,"byte_end":71534,"line_start":2014,"line_end":2014,"column_start":8,"column_end":50,"is_primary":true,"text":[{"text":"    fn TestParseLnVMware__test_invalid_event_type() {","highlight_start":8,"highlight_end":50}],"label":null,"suggested_replacement":"test_parse_ln_vmware_test_invalid_event_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnVMware__test_invalid_event_type` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:2014:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2014\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnVMware__test_invalid_event_type() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_vmware_test_invalid_event_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnVMware__test_invalid_alert_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":71582,"byte_end":71624,"line_start":2019,"line_end":2019,"column_start":8,"column_end":50,"is_primary":true,"text":[{"text":"    fn TestParseLnVMware__test_invalid_alert_type() {","highlight_start":8,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":71582,"byte_end":71624,"line_start":2019,"line_end":2019,"column_start":8,"column_end":50,"is_primary":true,"text":[{"text":"    fn TestParseLnVMware__test_invalid_alert_type() {","highlight_start":8,"highlight_end":50}],"label":null,"suggested_replacement":"test_parse_ln_vmware_test_invalid_alert_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnVMware__test_invalid_alert_type` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:2019:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2019\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnVMware__test_invalid_alert_type() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_vmware_test_invalid_alert_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnVMware__test_invalid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":71672,"byte_end":71708,"line_start":2024,"line_end":2024,"column_start":8,"column_end":44,"is_primary":true,"text":[{"text":"    fn TestParseLnVMware__test_invalid_line() {","highlight_start":8,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":71672,"byte_end":71708,"line_start":2024,"line_end":2024,"column_start":8,"column_end":44,"is_primary":true,"text":[{"text":"    fn TestParseLnVMware__test_invalid_line() {","highlight_start":8,"highlight_end":44}],"label":null,"suggested_replacement":"test_parse_ln_vmware_test_invalid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnVMware__test_invalid_line` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:2024:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2024\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnVMware__test_invalid_line() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_vmware_test_invalid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnWindowsServer__test_valid_line__ws_an_ad_pattern` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":72114,"byte_end":72173,"line_start":2041,"line_end":2041,"column_start":8,"column_end":67,"is_primary":true,"text":[{"text":"    fn TestParseLnWindowsServer__test_valid_line__ws_an_ad_pattern() {","highlight_start":8,"highlight_end":67}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":72114,"byte_end":72173,"line_start":2041,"line_end":2041,"column_start":8,"column_end":67,"is_primary":true,"text":[{"text":"    fn TestParseLnWindowsServer__test_valid_line__ws_an_ad_pattern() {","highlight_start":8,"highlight_end":67}],"label":null,"suggested_replacement":"test_parse_ln_windows_server_test_valid_line_ws_an_ad_pattern","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnWindowsServer__test_valid_line__ws_an_ad_pattern` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:2041:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2041\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnWindowsServer__test_valid_line__ws_an_ad_pattern() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_windows_server_test_valid_line_ws_an_ad_pattern`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnWindowsServer__test_valid_line__ws_sw_pattern` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":74148,"byte_end":74204,"line_start":2069,"line_end":2069,"column_start":8,"column_end":64,"is_primary":true,"text":[{"text":"    fn TestParseLnWindowsServer__test_valid_line__ws_sw_pattern() {","highlight_start":8,"highlight_end":64}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":74148,"byte_end":74204,"line_start":2069,"line_end":2069,"column_start":8,"column_end":64,"is_primary":true,"text":[{"text":"    fn TestParseLnWindowsServer__test_valid_line__ws_sw_pattern() {","highlight_start":8,"highlight_end":64}],"label":null,"suggested_replacement":"test_parse_ln_windows_server_test_valid_line_ws_sw_pattern","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnWindowsServer__test_valid_line__ws_sw_pattern` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:2069:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2069\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnWindowsServer__test_valid_line__ws_sw_pattern() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_windows_server_test_valid_line_ws_sw_pattern`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnWindowsServer__test_mapping_object_ip_to_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":75950,"byte_end":76013,"line_start":2097,"line_end":2097,"column_start":8,"column_end":71,"is_primary":true,"text":[{"text":"    fn TestParseLnWindowsServer__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":75950,"byte_end":76013,"line_start":2097,"line_end":2097,"column_start":8,"column_end":71,"is_primary":true,"text":[{"text":"    fn TestParseLnWindowsServer__test_mapping_object_ip_to_object_name() {","highlight_start":8,"highlight_end":71}],"label":null,"suggested_replacement":"test_parse_ln_windows_server_test_mapping_object_ip_to_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnWindowsServer__test_mapping_object_ip_to_object_name` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:2097:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2097\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnWindowsServer__test_mapping_object_ip_to_object_name() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_windows_server_test_mapping_object_ip_to_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnWindowsServer__test_invalid_object_name` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":77994,"byte_end":78044,"line_start":2125,"line_end":2125,"column_start":8,"column_end":58,"is_primary":true,"text":[{"text":"    fn TestParseLnWindowsServer__test_invalid_object_name() {","highlight_start":8,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":77994,"byte_end":78044,"line_start":2125,"line_end":2125,"column_start":8,"column_end":58,"is_primary":true,"text":[{"text":"    fn TestParseLnWindowsServer__test_invalid_object_name() {","highlight_start":8,"highlight_end":58}],"label":null,"suggested_replacement":"test_parse_ln_windows_server_test_invalid_object_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnWindowsServer__test_invalid_object_name` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:2125:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2125\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnWindowsServer__test_invalid_object_name() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_windows_server_test_invalid_object_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnWindowsServer__test_invalid_event_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":78971,"byte_end":79020,"line_start":2140,"line_end":2140,"column_start":8,"column_end":57,"is_primary":true,"text":[{"text":"    fn TestParseLnWindowsServer__test_invalid_event_type() {","highlight_start":8,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":78971,"byte_end":79020,"line_start":2140,"line_end":2140,"column_start":8,"column_end":57,"is_primary":true,"text":[{"text":"    fn TestParseLnWindowsServer__test_invalid_event_type() {","highlight_start":8,"highlight_end":57}],"label":null,"suggested_replacement":"test_parse_ln_windows_server_test_invalid_event_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnWindowsServer__test_invalid_event_type` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:2140:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2140\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnWindowsServer__test_invalid_event_type() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_windows_server_test_invalid_event_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnWindowsServer__test_invalid_alert_type` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":79068,"byte_end":79117,"line_start":2145,"line_end":2145,"column_start":8,"column_end":57,"is_primary":true,"text":[{"text":"    fn TestParseLnWindowsServer__test_invalid_alert_type() {","highlight_start":8,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":79068,"byte_end":79117,"line_start":2145,"line_end":2145,"column_start":8,"column_end":57,"is_primary":true,"text":[{"text":"    fn TestParseLnWindowsServer__test_invalid_alert_type() {","highlight_start":8,"highlight_end":57}],"label":null,"suggested_replacement":"test_parse_ln_windows_server_test_invalid_alert_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnWindowsServer__test_invalid_alert_type` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:2145:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2145\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnWindowsServer__test_invalid_alert_type() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_windows_server_test_invalid_alert_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `TestParseLnWindowsServer__test_invalid_line` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src/test_utils.rs","byte_start":79165,"byte_end":79208,"line_start":2150,"line_end":2150,"column_start":8,"column_end":51,"is_primary":true,"text":[{"text":"    fn TestParseLnWindowsServer__test_invalid_line() {","highlight_start":8,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src/test_utils.rs","byte_start":79165,"byte_end":79208,"line_start":2150,"line_end":2150,"column_start":8,"column_end":51,"is_primary":true,"text":[{"text":"    fn TestParseLnWindowsServer__test_invalid_line() {","highlight_start":8,"highlight_end":51}],"label":null,"suggested_replacement":"test_parse_ln_windows_server_test_invalid_line","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `TestParseLnWindowsServer__test_invalid_line` should have a snake case name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/test_utils.rs:2150:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2150\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn TestParseLnWindowsServer__test_invalid_line() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `test_parse_ln_windows_server_test_invalid_line`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"107 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 107 warnings emitted\u001b[0m\n\n"}
