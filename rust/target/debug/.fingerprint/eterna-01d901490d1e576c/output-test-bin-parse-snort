{"$message_type":"diagnostic","message":"expected `;`, found `println`","code":null,"level":"error","spans":[{"file_name":"src/bin/parse-snort.rs","byte_start":5921,"byte_end":5928,"line_start":192,"line_end":192,"column_start":5,"column_end":12,"is_primary":false,"text":[{"text":"    println!(\"\\nLines parsed per sensor:\");","highlight_start":5,"highlight_end":12}],"label":"unexpected token","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/parse-snort.rs","byte_start":5865,"byte_end":5865,"line_start":180,"line_end":180,"column_start":8,"column_end":8,"is_primary":true,"text":[{"text":"    XXX","highlight_start":8,"highlight_end":8}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"add `;` here","code":null,"level":"help","spans":[{"file_name":"src/bin/parse-snort.rs","byte_start":5865,"byte_end":5865,"line_start":180,"line_end":180,"column_start":8,"column_end":8,"is_primary":true,"text":[{"text":"    XXX","highlight_start":8,"highlight_end":8}],"label":null,"suggested_replacement":";","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: expected `;`, found `println`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/parse-snort.rs:180:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m180\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    XXX\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mhelp: add `;` here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m192\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    println!(\"\\nLines parsed per sensor:\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12munexpected token\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 1 previous error\u001b[0m\n\n"}
